#!/bin/bash

echo "Installing VLESS Client dependencies..."
echo

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "Error: Node.js is not installed."
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "Error: npm is not installed."
    echo "Please install npm (usually comes with Node.js)"
    exit 1
fi

# 安装依赖
if [ ! -d "node_modules" ]; then
    echo "Installing Node.js dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo
        echo "Error: Failed to install dependencies."
        exit 1
    fi
    echo
    echo "Dependencies installed successfully!"
else
    echo "Dependencies already installed."
fi

echo
echo "VLESS Client is ready to use!"
echo
echo "Next steps:"
echo "1. Edit config.json to set your VLESS server details"
echo "2. Run 'npm start' to start the client"
echo "3. Configure your applications to use SOCKS5 proxy: 127.0.0.1:1080"
echo