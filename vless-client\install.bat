@echo off
echo Installing VLESS Client dependencies...
echo.

cd /d "%~dp0"

if not exist "node_modules" (
    echo Installing Node.js dependencies...
    npm install
    if errorlevel 1 (
        echo.
        echo Error: Failed to install dependencies.
        echo Please make sure Node.js and npm are installed.
        pause
        exit /b 1
    )
    echo.
    echo Dependencies installed successfully!
) else (
    echo Dependencies already installed.
)

echo.
echo VLESS Client is ready to use!
echo.
echo Next steps:
echo 1. Edit config.json to set your VLESS server details
echo 2. Run "npm start" to start the client
echo 3. Configure your applications to use SOCKS5 proxy: 127.0.0.1:1080
echo.
pause