const VLESSClient = require('./vless-client');

// 配置示例
const config = {
    // VLESS服务器配置
    server: 'your-vless-server.com',  // 替换为你的VLESS服务器地址
    port: 443,                        // VLESS服务器端口
    uuid: 'd342d11e-d424-4583-b36e-524ab1f0afa4', // 替换为你的UUID
    path: '/',                        // WebSocket路径
    host: 'your-vless-server.com',    // Host头，通常与server相同
    tls: true,                        // 是否使用TLS
    
    // SOCKS代理配置
    socksPort: 1080                   // 本地SOCKS代理端口
};

// 创建并启动VLESS客户端
const client = new VLESSClient(config);
const server = client.startSOCKSProxy();

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\nShutting down VLESS client...');
    server.close(() => {
        console.log('SOCKS proxy server closed');
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('\nShutting down VLESS client...');
    server.close(() => {
        console.log('SOCKS proxy server closed');
        process.exit(0);
    });
});