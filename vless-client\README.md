# VLESS Client

基于Node.js的VLESS客户端实现，支持连接VLESS服务器并提供SOCKS5代理服务。

## 功能特性

- ✅ 完整的VLESS协议支持
- ✅ WebSocket传输层
- ✅ TLS/SSL加密支持
- ✅ SOCKS5代理服务器
- ✅ IPv4/IPv6/域名地址支持
- ✅ TCP连接代理

## 安装依赖

```bash
cd vless-client
npm install
```

## 配置

编辑 `index.js` 文件中的配置：

```javascript
const config = {
    // VLESS服务器配置
    server: 'your-vless-server.com',  // VLESS服务器地址
    port: 443,                        // VLESS服务器端口
    uuid: 'your-uuid-here',           // 你的UUID
    path: '/',                        // WebSocket路径
    host: 'your-vless-server.com',    // Host头
    tls: true,                        // 是否使用TLS
    
    // SOCKS代理配置
    socksPort: 1080                   // 本地SOCKS代理端口
};
```

## 使用方法

### 1. 启动客户端

```bash
npm start
```

或者使用开发模式（自动重启）：

```bash
npm run dev
```

### 2. 配置应用程序

启动后，客户端会在本地 `127.0.0.1:1080` 提供SOCKS5代理服务。

你可以配置浏览器或其他应用程序使用这个SOCKS5代理：

- **代理类型**: SOCKS5
- **代理地址**: 127.0.0.1
- **代理端口**: 1080

### 3. 浏览器配置示例

#### Chrome/Edge
使用命令行启动：
```bash
chrome.exe --proxy-server="socks5://127.0.0.1:1080"
```

#### Firefox
1. 打开设置 → 网络设置
2. 选择"手动代理配置"
3. SOCKS主机: 127.0.0.1，端口: 1080
4. 选择"SOCKS v5"

## 协议说明

### VLESS协议格式

客户端发送的VLESS请求包含以下字段：

```
[版本(1字节)] [UUID(16字节)] [附加信息长度(1字节)] [指令(1字节)] [端口(2字节)] [地址类型(1字节)] [地址] [数据]
```

- **版本**: 固定为 0x00
- **UUID**: 16字节的用户标识
- **附加信息长度**: 当前实现为 0x00
- **指令**: 0x01=TCP, 0x02=UDP
- **端口**: 目标端口，大端序
- **地址类型**: 0x01=IPv4, 0x02=域名, 0x03=IPv6
- **地址**: 根据地址类型编码的目标地址

### SOCKS5协议支持

客户端实现了完整的SOCKS5协议：

1. **认证阶段**: 支持无认证模式
2. **请求阶段**: 支持CONNECT命令
3. **数据转发**: 透明转发TCP数据

## 故障排除

### 常见问题

1. **连接失败**
   - 检查VLESS服务器地址和端口是否正确
   - 确认UUID是否匹配
   - 检查网络连接

2. **TLS错误**
   - 确认服务器是否支持TLS
   - 检查证书是否有效

3. **代理不工作**
   - 确认SOCKS代理配置正确
   - 检查本地端口是否被占用

### 调试模式

程序会输出详细的连接日志，包括：
- VLESS服务器连接状态
- SOCKS5客户端连接
- 数据转发状态

## 兼容性

- **Node.js**: 14.0+
- **操作系统**: Windows, macOS, Linux
- **VLESS服务器**: 兼容标准VLESS协议实现

## 许可证

MIT License