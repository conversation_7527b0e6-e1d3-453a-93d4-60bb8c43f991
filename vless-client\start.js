const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const VLESSClient = require('./vless-client');

// 读取 YAML 配置文件
function loadYamlConfig() {
    const configPath = path.join(__dirname, '..', 'config_cache.yaml');
    
    if (fs.existsSync(configPath)) {
        try {
            const configData = fs.readFileSync(configPath, 'utf8');
            const config = yaml.load(configData);
            
            // 筛选 VLESS 代理
            const vlessProxies = [];
            if (config.proxies && Array.isArray(config.proxies)) {
                config.proxies.forEach(proxy => {
                    if (proxy.type === 'vless') {
                        vlessProxies.push({
                            name: proxy.name,
                            server: proxy.server,
                            port: proxy.port,
                            uuid: proxy.uuid,
                            tls: proxy.tls || false,
                            servername: proxy.servername,
                            network: proxy.network || 'tcp',
                            path: proxy['ws-opts']?.path || '/',
                            host: proxy['ws-opts']?.headers?.Host || proxy.servername || proxy.server
                        });
                    }
                });
            }
            
            console.log(`Found ${vlessProxies.length} VLESS proxies in config_cache.yaml`);
            return vlessProxies;
        } catch (error) {
            console.error('Error reading YAML config file:', error.message);
            return [];
        }
    } else {
        console.log('YAML config file not found at:', configPath);
        return [];
    }
}

// 读取配置文件
function loadConfig() {
    const configPath = path.join(__dirname, 'config.json');
    
    if (fs.existsSync(configPath)) {
        try {
            const configData = fs.readFileSync(configPath, 'utf8');
            const config = JSON.parse(configData);
            
            return {
                server: config.vless.server,
                port: config.vless.port,
                uuid: config.vless.uuid,
                path: config.vless.path,
                host: config.vless.host,
                tls: config.vless.tls,
                socksPort: config.socks.port
            };
        } catch (error) {
            console.error('Error reading config file:', error.message);
            console.log('Using default configuration...');
        }
    } else {
        console.log('Config file not found, using default configuration...');
    }
    
    // 默认配置
    return {
        server: 'your-vless-server.com',
        port: 443,
        uuid: 'd342d11e-d424-4583-b36e-524ab1f0afa4',
        path: '/',
        host: 'your-vless-server.com',
        tls: true,
        socksPort: 1080
    };
}

// 验证配置
function validateConfig(config) {
    const errors = [];
    
    if (!config.server || config.server === 'your-vless-server.com') {
        errors.push('Please set a valid VLESS server address in config.json');
    }
    
    if (!config.uuid || config.uuid === 'd342d11e-d424-4583-b36e-524ab1f0afa4') {
        errors.push('Please set a valid UUID in config.json');
    }
    
    if (config.port < 1 || config.port > 65535) {
        errors.push('Invalid server port number');
    }
    
    if (config.socksPort < 1 || config.socksPort > 65535) {
        errors.push('Invalid SOCKS port number');
    }
    
    return errors;
}

// 选择代理节点
function selectProxy(proxies) {
    if (proxies.length === 0) {
        console.log('No VLESS proxies found, using default config...');
        return null;
    }
    
    // 显示可用的代理节点
    console.log('\nAvailable VLESS proxies:');
    proxies.forEach((proxy, index) => {
        console.log(`  ${index + 1}. ${proxy.name} (${proxy.server}:${proxy.port})`);
    });
    
    // 默认选择第一个代理
    const selectedProxy = proxies[0];
    console.log(`\nSelected proxy: ${selectedProxy.name}`);
    
    return selectedProxy;
}

// 转换代理配置为 VLESS 客户端配置
function convertProxyToConfig(proxy) {
    console.log('Converting proxy config:', proxy);
    return {
        server: proxy.server,
        port: proxy.port,
        uuid: proxy.uuid,
        path: proxy.path,
        host: proxy.host,
        tls: proxy.tls,
        socksPort: 1089
    };
}

// 主函数
function main() {
    console.log('VLESS Client v1.0.0');
    console.log('===================');
    
    // 加载 YAML 配置
    const vlessProxies = loadYamlConfig();
    
    // 选择代理节点
    const selectedProxy = selectProxy(vlessProxies);
    
    let config;
    if (selectedProxy) {
        // 使用从 YAML 中选择的代理
        config = convertProxyToConfig(selectedProxy);
    } else {
        // 使用默认配置
        config = loadConfig();
    }
    
    // 验证配置
    const errors = validateConfig(config);
    if (errors.length > 0) {
        console.error('Configuration errors:');
        errors.forEach(error => console.error(`  - ${error}`));
        console.log('\nPlease check your configuration and try again.');
        process.exit(1);
    }
    
    // 显示配置信息
    console.log('\nConfiguration:');
    console.log(`  VLESS Server: ${config.server}:${config.port}`);
    console.log(`  UUID: ${config.uuid}`);
    console.log(`  Path: ${config.path}`);
    console.log(`  Host: ${config.host}`);
    console.log(`  TLS: ${config.tls ? 'Enabled' : 'Disabled'}`);
    console.log(`  SOCKS Port: ${config.socksPort}`);
    console.log('');
    
    // 创建并启动客户端
    const client = new VLESSClient(config);
    const server = client.startSOCKSProxy();
    
    console.log('VLESS client started successfully!');
    console.log('You can now configure your applications to use the SOCKS5 proxy:');
    console.log(`  Proxy: 127.0.0.1:${config.socksPort}`);
    console.log('');
    console.log('Press Ctrl+C to stop the client.');
    
    // 优雅关闭
    process.on('SIGINT', () => {
        console.log('\nShutting down VLESS client...');
        server.close(() => {
            console.log('VLESS client stopped.');
            process.exit(0);
        });
    });
    
    process.on('SIGTERM', () => {
        console.log('\nShutting down VLESS client...');
        server.close(() => {
            console.log('VLESS client stopped.');
            process.exit(0);
        });
    });
}

// 启动应用
if (require.main === module) {
    main();
}

module.exports = { loadConfig, validateConfig, loadYamlConfig, selectProxy };