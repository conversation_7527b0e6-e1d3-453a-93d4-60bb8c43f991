const net = require('net');
const crypto = require('crypto');
const WebSocket = require('ws');
const { v4: uuidv4 } = require('uuid');

class VLESSClient {
    constructor(config) {
        this.config = {
            server: config.server || 'localhost',
            port: config.port || 443,
            uuid: config.uuid || uuidv4(),
            path: config.path || '/',
            host: config.host || config.server,
            tls: config.tls !== false,
            ...config
        };
        this.socksPort = config.socksPort || 1080;
    }

    // 创建VLESS请求头
    createVLESSHeader(targetHost, targetPort, isUDP = false) {
        const uuid = this.parseUUID(this.config.uuid);
        const version = Buffer.from([0]); // VLESS版本
        const optLength = Buffer.from([0]); // 附加信息长度
        const command = Buffer.from([isUDP ? 2 : 1]); // 1=TCP, 2=UDP
        
        // 端口 (大端序)
        const port = Buffer.allocUnsafe(2);
        port.writeUInt16BE(targetPort, 0);
        
        // 地址类型和地址
        let addressType, addressBuffer;
        if (this.isIPv4(targetHost)) {
            addressType = Buffer.from([1]); // IPv4
            addressBuffer = Buffer.from(targetHost.split('.').map(x => parseInt(x)));
        } else if (this.isIPv6(targetHost)) {
            addressType = Buffer.from([3]); // IPv6
            addressBuffer = this.parseIPv6(targetHost);
        } else {
            addressType = Buffer.from([2]); // 域名
            const hostBuffer = Buffer.from(targetHost, 'utf8');
            addressBuffer = Buffer.concat([Buffer.from([hostBuffer.length]), hostBuffer]);
        }
        
        return Buffer.concat([
            version,
            uuid,
            optLength,
            command,
            port,
            addressType,
            addressBuffer
        ]);
    }

    // 解析UUID字符串为16字节Buffer
    parseUUID(uuidStr) {
        const hex = uuidStr.replace(/-/g, '');
        return Buffer.from(hex, 'hex');
    }

    // 检查是否为IPv4地址
    isIPv4(ip) {
        const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
        return ipv4Regex.test(ip);
    }

    // 检查是否为IPv6地址
    isIPv6(ip) {
        const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
        return ipv6Regex.test(ip);
    }

    // 解析IPv6地址
    parseIPv6(ipv6) {
        const parts = ipv6.split(':');
        const buffer = Buffer.allocUnsafe(16);
        for (let i = 0; i < 8; i++) {
            const value = parseInt(parts[i] || '0', 16);
            buffer.writeUInt16BE(value, i * 2);
        }
        return buffer;
    }

    // 创建WebSocket连接到VLESS服务器
    async connectToVLESS(targetHost, targetPort, isUDP = false) {
        return new Promise((resolve, reject) => {
            const protocol = this.config.tls ? 'wss' : 'ws';
            const url = `${protocol}://${this.config.server}:${this.config.port}${this.config.path}`;
            
            const wsOptions = {
                headers: {
                    'Host': this.config.host,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            };

            if (!this.config.tls) {
                wsOptions.rejectUnauthorized = false;
            }

            const ws = new WebSocket(url, wsOptions);
            
            ws.on('open', () => {
                console.log(`Connected to VLESS server: ${this.config.server}:${this.config.port}`);
                
                // 发送VLESS请求头
                const vlessHeader = this.createVLESSHeader(targetHost, targetPort, isUDP);
                ws.send(vlessHeader);
                
                resolve(ws);
            });

            ws.on('error', (error) => {
                console.error('WebSocket connection error:', error);
                reject(error);
            });

            ws.on('close', () => {
                console.log('WebSocket connection closed');
            });
        });
    }

    // 处理SOCKS5连接
    async handleSOCKS5Connection(clientSocket) {
        let step = 'auth';
        let targetHost, targetPort;

        clientSocket.on('data', async (data) => {
            try {
                if (step === 'auth') {
                    // SOCKS5认证阶段
                    if (data[0] === 0x05) {
                        // 回复无需认证
                        clientSocket.write(Buffer.from([0x05, 0x00]));
                        step = 'request';
                    } else {
                        clientSocket.end();
                    }
                } else if (step === 'request') {
                    // SOCKS5请求阶段
                    if (data[0] === 0x05 && data[1] === 0x01) { // CONNECT命令
                        const addressType = data[3];
                        let addressStart = 4;
                        
                        if (addressType === 0x01) { // IPv4
                            targetHost = `${data[4]}.${data[5]}.${data[6]}.${data[7]}`;
                            addressStart = 8;
                        } else if (addressType === 0x03) { // 域名
                            const domainLength = data[4];
                            targetHost = data.slice(5, 5 + domainLength).toString();
                            addressStart = 5 + domainLength;
                        } else if (addressType === 0x04) { // IPv6
                            const ipv6Parts = [];
                            for (let i = 0; i < 8; i++) {
                                const part = data.readUInt16BE(4 + i * 2);
                                ipv6Parts.push(part.toString(16));
                            }
                            targetHost = ipv6Parts.join(':');
                            addressStart = 20;
                        }
                        
                        targetPort = data.readUInt16BE(addressStart);
                        
                        console.log(`SOCKS5 request: ${targetHost}:${targetPort}`);
                        
                        try {
                            // 连接到VLESS服务器
                            const ws = await this.connectToVLESS(targetHost, targetPort);
                            
                            // 发送SOCKS5成功响应
                            const response = Buffer.from([
                                0x05, 0x00, 0x00, 0x01,
                                0x00, 0x00, 0x00, 0x00,
                                0x00, 0x00
                            ]);
                            clientSocket.write(response);
                            
                            // 设置数据转发
                            this.setupDataForwarding(clientSocket, ws);
                            step = 'forwarding';
                            
                        } catch (error) {
                            console.error('Failed to connect to VLESS server:', error);
                            // 发送SOCKS5错误响应
                            const errorResponse = Buffer.from([
                                0x05, 0x01, 0x00, 0x01,
                                0x00, 0x00, 0x00, 0x00,
                                0x00, 0x00
                            ]);
                            clientSocket.write(errorResponse);
                            clientSocket.end();
                        }
                    } else {
                        clientSocket.end();
                    }
                }
            } catch (error) {
                console.error('Error handling SOCKS5 data:', error);
                clientSocket.end();
            }
        });

        clientSocket.on('error', (error) => {
            console.error('Client socket error:', error);
        });

        clientSocket.on('close', () => {
            console.log('Client disconnected');
        });
    }

    // 设置客户端和WebSocket之间的数据转发
    setupDataForwarding(clientSocket, ws) {
        let vlessResponseReceived = false;

        // 客户端 -> WebSocket
        clientSocket.on('data', (data) => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(data);
            }
        });

        // WebSocket -> 客户端
        ws.on('message', (data) => {
            if (!vlessResponseReceived) {
                // 跳过VLESS响应头 (版本1字节 + 附加信息长度1字节)
                const responseHeaderLength = 2;
                if (data.length > responseHeaderLength) {
                    const payload = data.slice(responseHeaderLength);
                    if (payload.length > 0) {
                        clientSocket.write(payload);
                    }
                }
                vlessResponseReceived = true;
            } else {
                clientSocket.write(data);
            }
        });

        // 处理连接关闭
        clientSocket.on('close', () => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        });

        ws.on('close', () => {
            if (!clientSocket.destroyed) {
                clientSocket.end();
            }
        });

        ws.on('error', (error) => {
            console.error('WebSocket error:', error);
            if (!clientSocket.destroyed) {
                clientSocket.end();
            }
        });
    }

    // 启动SOCKS代理服务器
    startSOCKSProxy() {
        const server = net.createServer((clientSocket) => {
            console.log('New SOCKS client connected');
            this.handleSOCKS5Connection(clientSocket);
        });

        server.listen(this.socksPort, '127.0.0.1', () => {
            console.log(`SOCKS5 proxy server listening on 127.0.0.1:${this.socksPort}`);
            console.log(`VLESS server: ${this.config.server}:${this.config.port}`);
            console.log(`UUID: ${this.config.uuid}`);
        });

        server.on('error', (error) => {
            console.error('SOCKS server error:', error);
        });

        return server;
    }
}

module.exports = VLESSClient;