const VLESSClient = require('./vless-client');

// 测试配置 - 连接到本地测试服务器
const testConfig = {
    server: 'localhost',
    port: 8080,
    uuid: 'd342d11e-d424-4583-b36e-524ab1f0afa4',
    path: '/',
    host: 'localhost',
    tls: false,  // 测试时不使用TLS
    socksPort: 1081  // 使用不同的端口避免冲突
};

console.log('Starting VLESS client test...');
console.log('Test configuration:', testConfig);

const client = new VLESSClient(testConfig);

// 测试VLESS头部创建
console.log('\n=== Testing VLESS header creation ===');
const testHeader = client.createVLESSHeader('www.google.com', 80);
console.log('VLESS header length:', testHeader.length);
console.log('VLESS header (hex):', testHeader.toString('hex'));

// 测试UUID解析
console.log('\n=== Testing UUID parsing ===');
const uuidBuffer = client.parseUUID(testConfig.uuid);
console.log('UUID buffer:', uuidBuffer.toString('hex'));
console.log('UUID buffer length:', uuidBuffer.length);

// 测试地址类型检测
console.log('\n=== Testing address type detection ===');
console.log('Is IPv4 (***********):', client.isIPv4('***********'));
console.log('Is IPv4 (www.google.com):', client.isIPv4('www.google.com'));
console.log('Is IPv6 (2001:db8::1):', client.isIPv6('2001:db8::1'));

// 启动测试服务器
console.log('\n=== Starting test SOCKS proxy ===');
const server = client.startSOCKSProxy();

console.log('\nTest server started. You can now:');
console.log('1. Configure your browser to use SOCKS5 proxy: 127.0.0.1:1081');
console.log('2. Try accessing websites through the proxy');
console.log('3. Press Ctrl+C to stop the test');

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\nStopping test server...');
    server.close(() => {
        console.log('Test server stopped');
        process.exit(0);
    });
});